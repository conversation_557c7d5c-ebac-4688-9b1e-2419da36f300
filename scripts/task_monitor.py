import sys
import asyncio
import time
import random
from typing import Dict, Any, Set

import websockets
import json
import os

# 获取当前脚本的文件路径
current_file_path = os.path.abspath(__file__)
# 获取当前脚本所在的目录
current_dir = os.path.dirname(current_file_path)
# 获取当前脚本所在目录的上一级目录
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from celery_manage.tasks import process_websocket_message, offline_resource
from conf.settings import DEFAULT_CLIENT_ID
from core.Log import ws_producer_logger, task_monitor_logger
from services.server_source import get_comfy_address_desc_map
from utils.utils import get_comfy_resources
from databases.redis import cache_redis

# 存储每个地址最后接收消息的时间和连接状态
connection_status: Dict[str, Any] = {}
# 存储当前活跃的WebSocket任务，用于动态管理连接
active_websocket_tasks: Dict[str, asyncio.Task] = {}
# 用于控制任务取消的事件
shutdown_events: Dict[str, asyncio.Event] = {}
timer = time.time


async def exponential_backoff(attempt: int, max_delay: int = 60) -> None:
    """
    指数退避策略，避免频繁重连

    Args:
        attempt: 当前重试次数
        max_delay: 最大延迟时间（秒）

    功能说明:
    使用指数退避算法计算重连延迟时间，避免在服务器故障时频繁重连
    延迟时间 = min(2^attempt + 随机数, max_delay)
    """
    delay = min(2 ** attempt + random.uniform(0, 1), max_delay)
    await asyncio.sleep(delay)


async def get_connection_status_summary():
    """
    获取所有连接的状态摘要，用于监控和调试

    Returns:
        dict: 包含连接状态统计信息的字典
    """
    total_connections = len(connection_status)
    active_connections = sum(1 for status in connection_status.values() if status.get('connected', False))
    inactive_connections = total_connections - active_connections

    current_time = timer()
    stale_connections = []

    for address, status in connection_status.items():
        last_message_time = status.get('last_message_time', 0)
        if current_time - last_message_time > 300:  # 5分钟没有消息
            stale_connections.append({
                'address': address,
                'last_message_ago': current_time - last_message_time,
                'connected': status.get('connected', False)
            })

    return {
        'total_connections': total_connections,
        'active_connections': active_connections,
        'inactive_connections': inactive_connections,
        'stale_connections': stale_connections,
        'active_tasks': len(active_websocket_tasks)
    }


async def work(address: str) -> None:
    """
    处理单个ComfyUI服务器的WebSocket连接

    Args:
        address: ComfyUI服务器地址 (格式: "ip:port")

    功能说明:
    1. 建立WebSocket连接到指定的ComfyUI服务器
    2. 监听服务器发送的消息并处理
    3. 实现自动重连机制，包含指数退避策略
    4. 监控连接健康状态，超时自动重连
    5. 连接失败达到阈值时自动下线服务器
    """
    ws_url = f"ws://{address}/ws?clientId={DEFAULT_CLIENT_ID}"
    attempt = 0

    # 为当前地址创建关闭事件，用于优雅关闭连接
    shutdown_events[address] = asyncio.Event()

    try:
        while not shutdown_events[address].is_set():
            try:
                task_monitor_logger.info(
                    f"Connecting to {comfy_address_desc_map.get(address, '')} websocket at {ws_url}")

                # 初始化连接状态
                connection_status[address] = {
                    'last_message_time': timer(),
                    'connected': False
                }

                # 建立WebSocket连接，设置ping间隔和超时
                async with websockets.connect(ws_url, ping_interval=10, ping_timeout=10) as websocket:
                    connection_status[address]['connected'] = True
                    attempt = 0  # 重置重试计数

                    # 启动消息监控任务，监控连接健康状态
                    monitor_task = asyncio.create_task(
                        message_monitor(address, timeout=180)
                    )

                    # 监听WebSocket消息
                    try:
                        await listen_to_websocket(websocket, address)
                    except Exception as e:
                        task_monitor_logger.error(f"WebSocket listening error for {address}: {e}")
                    finally:
                        # 取消监控任务
                        monitor_task.cancel()
                        try:
                            await monitor_task
                        except asyncio.CancelledError:
                            pass

            except (websockets.exceptions.ConnectionClosed,
                    websockets.exceptions.InvalidStatusCode,
                    websockets.exceptions.InvalidURI) as e:
                task_monitor_logger.error(f"WebSocket connection error for {address}: {e}")

                # 检查是否需要下线服务器
                if attempt >= 5:  # 连续失败5次后尝试下线服务器
                    task_monitor_logger.error(f"Too many connection failures for {address}, marking as offline")
                    try:
                        offline_resource.delay(address)
                    except Exception as offline_err:
                        task_monitor_logger.error(f"Failed to offline resource {address}: {offline_err}")

                # 更新连接状态
                connection_status[address]['connected'] = False

                # 使用指数退避策略避免频繁重连
                attempt += 1
                await exponential_backoff(attempt)

            except Exception as e:
                task_monitor_logger.error(f"Unexpected error for {address}: {e}")
                connection_status[address]['connected'] = False
                attempt += 1
                await exponential_backoff(attempt)

    finally:
        # 清理资源
        task_monitor_logger.info(f"WebSocket task for {address} is shutting down")
        if address in connection_status:
            connection_status[address]['connected'] = False
        if address in shutdown_events:
            del shutdown_events[address]


async def listen_to_websocket(websocket, address):
    """
    监听WebSocket消息并处理

    Args:
        websocket: WebSocket连接对象
        address: ComfyUI服务器地址

    功能说明:
    1. 持续监听WebSocket消息
    2. 更新连接的最后活跃时间
    3. 过滤并处理特定类型的消息
    4. 将重要消息发送到Celery任务队列进行异步处理
    """
    try:
        async for message in websocket:
            # 检查是否收到关闭信号
            if shutdown_events.get(address) and shutdown_events[address].is_set():
                task_monitor_logger.info(f"Received shutdown signal for {address}, closing WebSocket")
                break

            # 更新最后接收消息的时间，用于连接健康监控
            connection_status[address]['last_message_time'] = timer()

            # 只处理字符串类型的消息
            if not isinstance(message, str):
                continue

            try:
                message = json.loads(message)
            except json.JSONDecodeError:
                task_monitor_logger.warning(f"Invalid JSON message from {address}: {message}")
                continue

            msg_type = message.get('type')

            # 处理执行开始、状态更新、执行错误等消息
            if msg_type in ['execution_start', 'status', 'execution_error']:
                ws_producer_logger.info(f"producing: {message}")
                process_websocket_message.delay(message, address)
            # 处理执行中的消息，只处理任务完成的消息
            elif msg_type == 'executing':
                data = message.get('data', {})
                prompt_id = data.get('prompt_id', "")
                # node为None且有prompt_id表示任务执行完成
                if data.get('node') is None and data.get('prompt_id') == prompt_id:
                    ws_producer_logger.info(f"producing: {message}")
                    process_websocket_message.delay(message, address)

    except websockets.exceptions.ConnectionClosed as e:
        task_monitor_logger.warning(f"WebSocket connection closed for {address}: {e}")
        raise  # 重新抛出异常以触发重连


async def message_monitor(address, timeout=180):
    """
    监控消息接收，如果长时间没有消息则关闭连接触发重连

    Args:
        address: ComfyUI服务器地址
        timeout: 超时时间（秒），默认180秒

    功能说明:
    1. 定期检查连接的最后活跃时间
    2. 如果超过指定时间没有收到消息，则认为连接异常
    3. 触发连接重建以保证连接的可靠性
    """
    while True:
        # 检查是否收到关闭信号
        if shutdown_events.get(address) and shutdown_events[address].is_set():
            task_monitor_logger.info(f"Message monitor for {address} received shutdown signal")
            return

        current_time = timer()
        last_message_time = connection_status[address]['last_message_time']

        # 如果超过timeout时间没有收到消息，认为连接异常
        if current_time - last_message_time > timeout:
            task_monitor_logger.warning(
                f"No messages received from {address} for {timeout} seconds, reconnecting")
            # 直接返回让连接关闭，触发重连机制
            return

        # 每30秒检查一次连接状态
        await asyncio.sleep(30)


async def refresh_comfy_servers():
    """
    定期刷新ComfyUI服务器列表并动态管理WebSocket连接

    功能说明:
    1. 每5分钟从数据库获取最新的服务器列表
    2. 比较新旧服务器列表，识别新增和移除的服务器
    3. 为新增的服务器创建WebSocket连接任务
    4. 优雅关闭已移除服务器的WebSocket连接
    5. 更新缓存中的活跃服务器列表
    """
    while True:
        try:
            global comfy_address_desc_map

            # 从数据库获取最新的服务器资源信息（不使用缓存）
            resource_info_list = await get_comfy_resources(use_cache=False)
            # 提取状态为1（活跃）的服务器地址列表
            new_comfy_addr_set = set(i['address'] for i in resource_info_list if i.get('status', 0) == 1)
            # 更新服务器地址到描述的映射
            comfy_address_desc_map = await get_comfy_address_desc_map()

            # 获取当前正在运行的WebSocket任务的服务器地址集合
            current_addr_set = set(active_websocket_tasks.keys())

            # 识别需要新增连接的服务器（在新列表中但不在当前任务中）
            servers_to_add = new_comfy_addr_set - current_addr_set
            # 识别需要移除连接的服务器（在当前任务中但不在新列表中）
            servers_to_remove = current_addr_set - new_comfy_addr_set

            # 为新增的服务器创建WebSocket连接任务
            for address in servers_to_add:
                task_monitor_logger.info(f"Adding WebSocket connection for new server: {address}")
                task = asyncio.create_task(work(address))
                active_websocket_tasks[address] = task

            # 优雅关闭已移除服务器的WebSocket连接
            for address in servers_to_remove:
                task_monitor_logger.info(f"Removing WebSocket connection for offline server: {address}")

                # 设置关闭事件，通知WebSocket任务优雅退出
                if address in shutdown_events:
                    shutdown_events[address].set()

                # 取消并等待任务完成
                if address in active_websocket_tasks:
                    task = active_websocket_tasks[address]
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
                    # 从活跃任务字典中移除
                    del active_websocket_tasks[address]

                # 清理连接状态
                if address in connection_status:
                    del connection_status[address]

            # 存储当前活跃的服务器列表到Redis缓存
            cache_redis.set('active_comfy_servers', json.dumps(list(new_comfy_addr_set)), ex=300)

            task_monitor_logger.info(
                f"Refreshed ComfyUI server list: {len(new_comfy_addr_set)} active servers, "
                f"added {len(servers_to_add)}, removed {len(servers_to_remove)}")

        except Exception as e:
            task_monitor_logger.error(f"Failed to refresh ComfyUI servers: {e}")

        # 每5分钟刷新一次服务器列表
        await asyncio.sleep(300)


async def main():
    """
    主函数，启动所有任务

    功能说明:
    1. 初始化ComfyUI服务器信息和连接映射
    2. 为初始的活跃服务器创建WebSocket监控任务
    3. 启动服务器列表刷新任务，实现动态管理
    4. 优雅处理程序退出，确保所有连接正确关闭
    """
    global comfy_address_desc_map

    task_monitor_logger.info("Starting ComfyUI WebSocket monitor...")

    try:
        # 初始化服务器信息
        resource_info_list = await get_comfy_resources(use_cache=False)
        comfy_addr_list = list({i['address'] for i in resource_info_list if i.get('status', 0) == 1})
        comfy_address_desc_map = await get_comfy_address_desc_map()

        task_monitor_logger.info(f"Found {len(comfy_addr_list)} initial active servers")

        # 为每个初始服务器创建WebSocket监控任务
        for address in comfy_addr_list:
            task = asyncio.create_task(work(address))
            active_websocket_tasks[address] = task
            task_monitor_logger.info(f"Started WebSocket task for server: {address}")

        # 启动服务器列表刷新任务，负责动态管理WebSocket连接
        refresh_task = asyncio.create_task(refresh_comfy_servers())

        # 等待刷新任务完成（实际上会一直运行）
        await refresh_task

    except KeyboardInterrupt:
        task_monitor_logger.info("Received interrupt signal, shutting down gracefully...")
    except Exception as e:
        task_monitor_logger.error(f"Error in main task: {e}")
    finally:
        # 优雅关闭所有连接
        task_monitor_logger.info("Shutting down all WebSocket connections...")

        # 设置所有服务器的关闭事件
        for address in list(shutdown_events.keys()):
            shutdown_events[address].set()

        # 取消所有WebSocket任务
        for address, task in list(active_websocket_tasks.items()):
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass

        # 取消刷新任务
        if 'refresh_task' in locals():
            refresh_task.cancel()
            try:
                await refresh_task
            except asyncio.CancelledError:
                pass

        task_monitor_logger.info("All tasks have been shut down")


if __name__ == "__main__":
    asyncio.run(main())
