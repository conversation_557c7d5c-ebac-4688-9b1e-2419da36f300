import sys
import asyncio
import time
import random
from typing import Dict, Any

import websockets
import json
import os

# 获取当前脚本的文件路径
current_file_path = os.path.abspath(__file__)
# 获取当前脚本所在的目录
current_dir = os.path.dirname(current_file_path)
# 获取当前脚本所在目录的上一级目录
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from celery_manage.tasks import process_websocket_message, offline_resource
from conf.settings import DEFAULT_CLIENT_ID
from core.Log import ws_producer_logger, task_monitor_logger
from services.server_source import get_comfy_address_desc_map
from utils.utils import get_comfy_resources
from databases.redis import cache_redis

# 存储每个地址最后接收消息的时间
connection_status: Dict[str, Any] = {}
timer = time.time


async def exponential_backoff(attempt: int, max_delay: int = 60) -> None:
    """指数退避策略，避免频繁重连"""
    delay = min(2 ** attempt + random.uniform(0, 1), max_delay)
    await asyncio.sleep(delay)


async def work(address: str) -> None:
    """处理单个ComfyUI服务器的WebSocket连接"""
    ws_url = f"ws://{address}/ws?clientId={DEFAULT_CLIENT_ID}"
    attempt = 0
    
    while True:
        try:
            task_monitor_logger.info(
                f"Connecting to {comfy_address_desc_map.get(address, '')} websocket at {ws_url}")
            
            # 初始化连接状态
            connection_status[address] = {
                'last_message_time': timer(),
                'connected': False
            }
            
            async with websockets.connect(ws_url, ping_interval=10, ping_timeout=10) as websocket:
                connection_status[address]['connected'] = True
                attempt = 0  # 重置重试计数
                
                # 启动消息监控任务
                monitor_task = asyncio.create_task(
                    message_monitor(address, timeout=180)
                )
                
                # 监听WebSocket消息
                try:
                    await listen_to_websocket(websocket, address)
                except Exception as e:
                    task_monitor_logger.error(f"WebSocket listening error for {address}: {e}")
                finally:
                    # 取消监控任务
                    monitor_task.cancel()
                    try:
                        await monitor_task
                    except asyncio.CancelledError:
                        pass
        
        except (websockets.exceptions.ConnectionClosed, 
                websockets.exceptions.InvalidStatusCode,
                websockets.exceptions.InvalidURI) as e:
            task_monitor_logger.error(f"WebSocket connection error for {address}: {e}")
            
            # 检查是否需要下线服务器
            if attempt >= 5:  # 连续失败5次后尝试下线服务器
                task_monitor_logger.error(f"Too many connection failures for {address}, marking as offline")
                try:
                    offline_resource.delay(address)
                except Exception as offline_err:
                    task_monitor_logger.error(f"Failed to offline resource {address}: {offline_err}")
            
            # 更新连接状态
            connection_status[address]['connected'] = False
            
            # 使用指数退避策略
            attempt += 1
            await exponential_backoff(attempt)
            
        except Exception as e:
            task_monitor_logger.error(f"Unexpected error for {address}: {e}")
            connection_status[address]['connected'] = False
            attempt += 1
            await exponential_backoff(attempt)


async def listen_to_websocket(websocket, address):
    """监听websocket消息并处理"""
    try:
        async for message in websocket:
            # 更新最后接收消息的时间
            connection_status[address]['last_message_time'] = timer()
            
            if not isinstance(message, str):
                continue
                
            message = json.loads(message)
            msg_type = message.get('type')
            
            if msg_type in ['execution_start', 'status', 'execution_error']:
                ws_producer_logger.info(f"producing: {message}")
                process_websocket_message.delay(message, address)
            elif msg_type == 'executing':
                data = message.get('data', {})
                prompt_id = data.get('prompt_id', "")
                if data.get('node') is None and data.get('prompt_id') == prompt_id:
                    ws_producer_logger.info(f"producing: {message}")
                    process_websocket_message.delay(message, address)
    
    except websockets.exceptions.ConnectionClosed as e:
        task_monitor_logger.warning(f"WebSocket connection closed for {address}: {e}")
        raise  # 重新抛出异常以触发重连


async def message_monitor(address, timeout=180):
    """监控消息接收，如果长时间没有消息则关闭连接触发重连"""
    while True:
        current_time = timer()
        last_message_time = connection_status[address]['last_message_time']
        
        # 如果超过timeout时间没有收到消息
        if current_time - last_message_time > timeout:
            task_monitor_logger.warning(
                f"No messages received from {address} for {timeout} seconds, reconnecting")
            # 不再使用异常控制流，而是直接返回让连接关闭
            return
            
        # 每30秒检查一次
        await asyncio.sleep(30)


async def refresh_comfy_servers():
    """定期刷新ComfyUI服务器列表"""
    while True:
        try:
            global comfy_address_desc_map
            resource_info_list = await get_comfy_resources(use_cache=False)
            comfy_addr_list = list({i['address'] for i in resource_info_list if i.get('status', 0) == 1})
            comfy_address_desc_map = await get_comfy_address_desc_map()
            
            # 存储当前活跃的服务器列表
            cache_redis.set('active_comfy_servers', json.dumps(comfy_addr_list), ex=300)
            
            task_monitor_logger.info(f"Refreshed ComfyUI server list, found {len(comfy_addr_list)} active servers")
        except Exception as e:
            task_monitor_logger.error(f"Failed to refresh ComfyUI servers: {e}")
        
        # 每5分钟刷新一次
        await asyncio.sleep(300)


async def main():
    """主函数，启动所有任务"""
    global comfy_address_desc_map
    
    # 初始化服务器信息
    resource_info_list = await get_comfy_resources(use_cache=False)
    comfy_addr_list = list({i['address'] for i in resource_info_list if i.get('status', 0) == 1})
    comfy_address_desc_map = await get_comfy_address_desc_map()
    
    # 启动服务器刷新任务
    refresh_task = asyncio.create_task(refresh_comfy_servers())
    
    # 为每个服务器创建监控任务
    server_tasks = [asyncio.create_task(work(address)) for address in comfy_addr_list]
    
    try:
        # 等待所有任务完成（实际上不会完成，除非发生错误）
        await asyncio.gather(*server_tasks)
    except Exception as e:
        task_monitor_logger.error(f"Error in main task: {e}")
    finally:
        # 取消刷新任务
        refresh_task.cancel()


if __name__ == "__main__":
    asyncio.run(main())
